import axios from 'axios';

/**
 * Performs user login and returns token and user data
 * @param {string} userId - The user ID
 * @param {string} password - The user password
 * @returns {Promise<Object>} - Object containing token and user data
 */
export const loginUser = async (userId, password) => {
  try {
    const response = await axios.post(
      'https://retailuat.abisaio.com:9001/api/Login/Post',
      {
        email: 'email', // This seems to be a fixed value in the original code
        userId: userId,
        password: password,
      }
    );

    // Check if response data exists and has the expected format
    if (response.data && response.data.token) {
      return {
        token: response.data.token,
        user: response.data.user
      };
    } else {
      throw new Error('Invalid response format from login API');
    }
  } catch (error) {
    console.error('Error during login:', error);
    throw error; // Re-throw to handle in the UI layer
  }
};

/**
 * Fetches branches accessible to a user
 * @param {string} empId - The employee ID
 * @param {string} token - The authentication token
 * @returns {Promise<Array>} - Array of branch objects
 */
export const fetchUserBranches = async (empId, token) => {
  try {
    // Format the employee ID with leading zeros if needed
    const formattedEmpId = empId.length < 10 ? `00${empId}` : empId;
    
    const response = await axios.get(
      `https://retailuat.abisibg.com/api/v1/branchaccess?EmpId=${formattedEmpId}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    // Check if response data exists and has the expected format
    if (response.data && Array.isArray(response.data)) {
      return response.data;
    } else {
      console.error('Invalid response format from branch access API:', response.data);
      return [];
    }
  } catch (error) {
    console.error('Error fetching user branches:', error);
    throw error; // Re-throw to handle in the UI layer
  }
};
