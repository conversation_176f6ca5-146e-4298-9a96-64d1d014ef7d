import React, { useState, useEffect } from 'react';
import { useNavigation, useRoute } from '@react-navigation/native';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    Modal,
    TouchableWithoutFeedback,
} from 'react-native';

import { fetchBusinessDay } from '../apiHandling/businessDayAPI';
import { bearerToken, loginBranchID, loginBranchName } from '../globals';

const Navbar = () => {
    const [currentTime, setCurrentTime] = useState('');
    const [selectedMenu, setSelectedMenu] = useState('');
    const [selectedSubOption, setSelectedSubOption] = useState('');
    const [showDropdown, setShowDropdown] = useState(false);
    const [showExitModal, setShowExitModal] = useState(false);
    const [expandedStates, setExpandedStates] = useState({});
    const [dropdownContent, setDropdownContent] = useState([]);
    const [businessDate, setBusinessDate] = useState('Loading...');
   

    const navigation = useNavigation();
    const route = useRoute();


    const getBusinessDay = async () => {
        try {
            // Use token and branchId from globals.js
            const formattedDate = await fetchBusinessDay(bearerToken, loginBranchID);
            setBusinessDate(formattedDate);
        } catch (error) {
            console.error('Error in getBusinessDay:', error);
            setBusinessDate(new Date().toLocaleDateString());
        }
    };

    const menuOptions = [
        'Billing',
        'Stock',
        'Logistics',
        'Finance',
        'HR',
        'Utils',
        'Reports',
        'Exit',
    ];

    const stockDropdownOptions = [
        { title: 'Receiving', subOptions: ['Receiving', 'Wheat', 'Sugar'] },
        { title: 'Finished Goods', subOptions: ['Basmati Rice', 'Bread', 'Cookies'] },
        { title: 'Packaging', subOptions: ['Bags', 'Labels'] },
    ];

    const financeDropdownOptions = [
        { title: 'Bank Transfer', subOptions: [] },
        { title: 'IB Branch Transfer', subOptions: [] },
        { title: 'HO Transfer', subOptions: [] },
        { title: 'Payments', subOptions: [] },
    ];

    const allStockSubOptions = stockDropdownOptions.flatMap(opt => opt.subOptions);
    const allFinanceSubOptions = financeDropdownOptions.flatMap(opt => opt.subOptions);

    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentTime(new Date().toLocaleTimeString());
        }, 1000);
        return () => clearInterval(interval);
    }, []);

    useEffect(() => {
        getBusinessDay();
    }, []);

    useEffect(() => {
        if (route.name) {
            if (allStockSubOptions.includes(route.name)) {
                setSelectedMenu('Stock');
                setSelectedSubOption(route.name);
            } else if (allFinanceSubOptions.includes(route.name)) {
                setSelectedMenu('Finance');
                setSelectedSubOption(route.name);
            } else {
                setSelectedMenu(route.name);
                setSelectedSubOption('');
            }
        }
    }, [route.name]);

    const handleMenuPress = (option) => {
        if (option === 'Exit') {
            setShowExitModal(true);
        } else if (option === 'Stock') {
            setSelectedMenu(option);
            setSelectedSubOption('');
            setShowDropdown(true);
            // Set dropdown content to stock options
            setDropdownContent(stockDropdownOptions);
        } else if (option === 'Finance') {
            setSelectedMenu(option);
            setSelectedSubOption('');
            setShowDropdown(true);
            // Set dropdown content to finance options
            setDropdownContent(financeDropdownOptions);
        } else {
            setSelectedMenu(option);
            setSelectedSubOption('');
            setShowDropdown(false);
            navigation.navigate(option);
        }
    };

    const handleOptionPress = (option) => {
        // For both Stock and Finance options, toggle the expanded state
        // If the option has no suboptions and it's a Finance option, navigate directly
        if (option.subOptions && option.subOptions.length > 0) {
            // Toggle expanded state for options with suboptions
            setExpandedStates(prev => ({
                ...prev,
                [option.title]: !prev[option.title],
            }));
        } else if (selectedMenu === 'Finance') {
            // For Finance options without suboptions, navigate directly
            setShowDropdown(false);

            // Map the option title to the correct screen name
            let screenName;
            switch(option.title) {
                case 'Bank Transfer':
                    screenName = 'BankTransfer';
                    break;
                case 'IB Branch Transfer':
                    screenName = 'IBBranchTransfer';
                    break;
                case 'HO Transfer':
                    screenName = 'HOTransfer';
                    break;
                case 'Payments':
                    screenName = 'Payments';
                    break;
                default:
                    screenName = option.title.replace(/\s+/g, '');
            }

            navigation.navigate(screenName);
        }
    };

    const handleSubOptionPress = (sub) => {
        if (allStockSubOptions.includes(sub)) {
            setSelectedMenu('Stock');
        }
        setSelectedSubOption(sub);
        setShowDropdown(false);
        navigation.navigate(sub);
    };

    return (
        <View style={styles.container}>
            <View style={styles.appBar}>
                <View style={styles.topRow}>
                    <Text style={styles.topText}>{businessDate}</Text>
                    <Text style={styles.topText}>{currentTime}</Text>
                    <Text style={styles.topText}>{`${loginBranchID}, ${loginBranchName}`}</Text>
                </View>

                <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.menuRow}>
                    {menuOptions.map(option => {
                        const isSelected = selectedMenu === option;
                        const isExit = option === 'Exit';
                        return (
                            <TouchableOpacity
                                key={option}
                                onPress={() => handleMenuPress(option)}
                                style={[styles.menuButton, {
                                    backgroundColor: isSelected
                                        ? isExit ? '#FF3333' : '#FDC500'
                                        : isExit ? '#FF3333' : '#02096A'
                                }]}
                            >
                                <Text style={[styles.menuText, { color: isSelected ? 'black' : 'white' }]}>{option}</Text>
                            </TouchableOpacity>
                        );
                    })}
                </ScrollView>
            </View>

            {/* Menu Dropdown */}
            <Modal transparent visible={showDropdown} animationType="fade">
                <TouchableWithoutFeedback onPress={() => setShowDropdown(false)}>
                    <View style={styles.overlay}>
                        <TouchableWithoutFeedback>
                            <View style={[
                                styles.dropdownContainer,
                                {
                                    left: selectedMenu === 'Finance' ?
                                        // Position for Finance dropdown
                                        200 :
                                        // Position for Stock dropdown
                                        100
                                }
                            ]}>
                                <ScrollView>
                                    {/* Same dropdown design for both Stock and Finance */}
                                    {dropdownContent.map(option => (
                                        <View key={option.title}>
                                            <TouchableOpacity onPress={() => handleOptionPress(option)} style={styles.dropdownItem}>
                                                <Text style={styles.dropdownText}>{option.title}</Text>
                                                {option.subOptions && option.subOptions.length > 0 && (
                                                    <Text style={styles.expandArrow}>{expandedStates[option.title] ? '▲' : '▼'}</Text>
                                                )}
                                            </TouchableOpacity>
                                            {expandedStates[option.title] && option.subOptions && option.subOptions.map(sub => (
                                                <TouchableOpacity key={sub} onPress={() => handleSubOptionPress(sub)} style={styles.subOption}>
                                                    <Text style={styles.subOptionText}>{sub}</Text>
                                                </TouchableOpacity>
                                            ))}
                                        </View>
                                    ))}
                                </ScrollView>
                            </View>
                        </TouchableWithoutFeedback>
                    </View>
                </TouchableWithoutFeedback>
            </Modal>

            {/* Exit Modal */}
            <Modal transparent visible={showExitModal} animationType="fade">
                <TouchableWithoutFeedback onPress={() => setShowExitModal(false)}>
                    <View style={styles.overlay}>
                        <TouchableWithoutFeedback>
                            <View style={styles.exitModalContainer}>
                                <Text style={styles.exitModalText}>Are you sure you want to exit?</Text>
                                <View style={styles.modalButtonRow}>
                                    <TouchableOpacity style={styles.modalButton} onPress={() => {
                                        setShowExitModal(false);
                                        navigation.navigate('Login');
                                    }}>
                                        <Text style={styles.modalButtonText}>Yes</Text>
                                    </TouchableOpacity>
                                    <TouchableOpacity style={[styles.modalButton, { backgroundColor: '#aaa' }]} onPress={() => setShowExitModal(false)}>
                                        <Text style={styles.modalButtonText}>No</Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </TouchableWithoutFeedback>
                    </View>
                </TouchableWithoutFeedback>
            </Modal>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 0,
    },
    appBar: {
        backgroundColor: '#02096A',
        height: 120,
        paddingTop: 20,
        paddingBottom: 0,
    },
    topRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 20,
        marginBottom: 10,
    },
    topText: { color: 'white', fontWeight: 'bold', fontSize: 15 },
    menuRow: { paddingHorizontal: 5, alignItems: 'center' },
    menuButton: { paddingHorizontal: 10, paddingVertical: 5, marginHorizontal: 5, borderRadius: 5 },
    menuText: { fontSize: 25, fontWeight: 'bold' },
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.73)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    dropdownContainer: {
        position: 'absolute',
        backgroundColor: '#FDC500',
        marginTop: 0,
        padding: 10,
        minWidth: 200,
        borderRadius: 8,
        elevation: 6,
        maxHeight: 400,
        top: 45, // Position right below the navbar
        zIndex: 1000,
    },
    dropdownItem: {
        paddingVertical: 10,
        paddingHorizontal: 8,
        flexDirection: 'row',
        justifyContent: 'space-between',
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },

    dropdownText: { fontSize: 16, fontWeight: '600' },
    expandArrow: { fontSize: 16 },
    subOption: { paddingLeft: 20, paddingVertical: 6 },
    subOptionText: { fontSize: 14 },
    exitModalContainer: {
        backgroundColor: 'white',
        padding: 20,
        borderRadius: 8,
        elevation: 10,
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: [{ translateX: -150 }, { translateY: -100 }],
        width: 300,
        height: 200,
    },
    exitModalText: { fontSize: 18, marginBottom: 20, textAlign: 'center' },
    modalButtonRow: { flexDirection: 'row', justifyContent: 'space-around', width: '100%' },
    modalButton: {
        paddingVertical: 10,
        paddingHorizontal: 20,
        backgroundColor: '#FDC500',
        borderRadius: 5,
        marginHorizontal: 10,
    },
    modalButtonText: { fontSize: 16, fontWeight: 'bold' },
});

export default Navbar;
