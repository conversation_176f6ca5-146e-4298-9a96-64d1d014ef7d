// src/navigation/AppNavigator.js
import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import SplashScreen from '../login/SplashScreen';
import LoginScreen from '../login/LoginScreen';
import Navbar from '../components/Navbar';
import BillingScreen from '../screen/billing/BillingScreen';
import BankTransferScreen from '../screen/finance/BankTransferScreen';
import IBBranchTransferScreen from '../screen/finance/IBBranchTransferScreen';
import HOTransferScreen from '../screen/finance/HOTransferScreen';
import PaymentScreen from '../screen/finance/PaymentScreen'; 
import DayEndScreen from '../screen/utils/DayEndScreen';
import DayRateScreen from '../screen/reports/DayRate';
import StockReportScreen from '../screen/reports/StockReportScreen';
import LedgerScreen from '../screen/reports/Ledger';
import DayReportScreen from '../screen/reports/DayReport';

import BillingPaymentScreen from '../screen/billing/BillingPaymentScreen';
import ViewBillScreen from '../screen/billing/ViewBillScreen';
import ReceivingScreen from '../screen/stock/ReceivingScreen';



const Stack = createNativeStackNavigator();

const AppNavigator = () => {
  return (
    <Stack.Navigator initialRouteName="BankTransfer" screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Splash" component={SplashScreen} />
      <Stack.Screen name="Login" component={LoginScreen} />
  
      <Stack.Screen name="Billing" component={BillingScreen} />
      <Stack.Screen name="BillingPayment" component={BillingPaymentScreen} />
      <Stack.Screen name="ViewBillScreen" component={ViewBillScreen} />
      <Stack.Screen name="Receiving" component={ReceivingScreen} />

      {/* Finance Screens */}
      <Stack.Screen name="BankTransfer" component={BankTransferScreen} />
      <Stack.Screen name="IBBranchTransfer" component={IBBranchTransferScreen} />
      <Stack.Screen name="HOTransfer" component={HOTransferScreen} />
      <Stack.Screen name="Payments" component={PaymentScreen} />

      {/* Utils Screens */}
      <Stack.Screen name="DayEnd" component={DayEndScreen} />
      <Stack.Screen name="Day_Sync" component={IBBranchTransferScreen} />
      <Stack.Screen name="Checklist" component={HOTransferScreen} />

      {/* Report Screens */}
      <Stack.Screen name="DayRate" component={DayRateScreen} />
      <Stack.Screen name="StockReport" component={StockReportScreen} />
      <Stack.Screen name="Ledger" component={LedgerScreen} />
      <Stack.Screen name="DayReport" component={DayReportScreen} />
    </Stack.Navigator>
  );
};

export default AppNavigator;
