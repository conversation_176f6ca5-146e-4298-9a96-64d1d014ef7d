import React from 'react';
import { View, Text, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useNavigation } from '@react-navigation/native';


const ViewBillScreen = ({ route }) => {

    const navigation = useNavigation();

    const {
        saleID,
        printDateTime,
        currentTimeStamp,
        itemTableDetails,
        deliveryCharges,
        roundOffAmount,
        totalAmount,
        discountAmount,
        selectedCustomerType,
        selectedGatewayName,
        customerID,
        customerName,
        customerPhoneNumber,
        customerAddress,
        customerPlace,
        customerCity,
        customerState,
        customerPincode,
    } = route.params;

    const hideNosColumn = itemTableDetails.every(item => (item.AltQty ?? 0) === 0);
    const hideWeightColumn = itemTableDetails.every(item => (item.Qty ?? 0) === 0);
    const hideGSTColumn = itemTableDetails.every(item => (item.TaxAmount ?? 0) === 0);

    const calculateTaxDetails = (items) => {
        const taxBreakups = [];

        items.forEach(item => {
            const cgstPercent = item.CGSTPercent || 0;
            const sgstPercent = item.SGSTPercent || 0;
            const cgstAmount = item.CGSTAmount || 0;
            const sgstAmount = item.SGSTAmount || 0;

            if (cgstAmount !== 0 || sgstAmount !== 0) {
                taxBreakups.push({
                    cgstPercent,
                    sgstPercent,
                    cgstAmount,
                    sgstAmount,
                });
            }
        });

        return { taxBreakups };
    };

    const taxDetails = calculateTaxDetails(itemTableDetails);

    return (
        <ScrollView style={styles.container}>
            <View style={styles.backButtonContainer}>
                <TouchableOpacity onPress={() => navigation.navigate('Billing')}>
                    <Icon name="arrow-back" size={24} color="black" />
                </TouchableOpacity>
            </View>


            <Text style={styles.title}>ABIS Exports India Pvt Ltd</Text>
            <Text style={styles.center}>TIKRAPARA SHOP</Text>
            <Text style={styles.center}>R156 NEAR GOOD WILL HOSPITAL</Text>
            <Text style={styles.center}>SIDDHARTH CHOWK</Text>
            <Text style={styles.center}>Kalibadi, Raipur, Chhattisgarh, PIN-492001</Text>
            <Text style={styles.center}>Shop No. : 11122-22333</Text>
            <Text style={styles.center}>FSSAI License Number: 10519016000172, GSTIN : 111222333444555</Text>
            <Text style={styles.center}>Duplicate bill</Text>

            <View style={styles.rowSpaceBetween}>
                <Text>POS/Bill No: {saleID}</Text>
                <Text>Print Date: {printDateTime}</Text>
            </View>
            <View style={styles.rowRight}>
                <Text>Business Date: {currentTimeStamp}</Text>
            </View>

            <Text style={styles.sectionHeader}>Item Details:</Text>
            <View style={styles.table}>
                <View style={styles.tableRow}>
                    <Text style={styles.cell}>Item name</Text>
                    <Text style={styles.cell}>Qty</Text>
                    <Text style={styles.cell}>Rate</Text>
                    {!hideGSTColumn && <Text style={styles.cell}>GST</Text>}
                    <Text style={styles.cell}>Total</Text>
                </View>
                {itemTableDetails.map((item, index) => (
                    <View style={styles.tableRow} key={index}>
                        <Text style={styles.cell}>{item.ItemName}</Text>
                        <Text style={styles.cell}>{item.Qty !== 0 ? item.Qty : item.AltQty}</Text>
                        <Text style={styles.cell}>{item.Rate}</Text>
                        {!hideGSTColumn && <Text style={styles.cell}>{item.TaxAmount}</Text>}
                        <Text style={styles.cell}>{item.TotalAmount}</Text>
                    </View>
                ))}
            </View>

            {deliveryCharges !== 0 && (
                <View style={styles.rowSpaceBetween}>
                    <Text>Freight:</Text>
                    <Text>₹{deliveryCharges}</Text>
                </View>
            )}

            <View style={styles.rowSpaceBetween}>
                <Text>Roundoff:</Text>
                <Text>₹{roundOffAmount}</Text>
            </View>

            <View style={styles.rowSpaceBetween}>
                <Text style={styles.bold}>Total (incl. GST):</Text>
                <Text style={styles.bold}>₹{totalAmount}</Text>
            </View>

            {selectedCustomerType !== 'CS' && (
                <>
                    {selectedGatewayName === 'Cash' && (
                        <View style={styles.rowSpaceBetween}>
                            <Text>Cash Paid:</Text>
                            <Text>₹{totalAmount}</Text>
                        </View>
                    )}
                    {selectedGatewayName === 'UPI' && (
                        <View style={styles.rowSpaceBetween}>
                            <Text>UPI:</Text>
                            <Text>₹{totalAmount}</Text>
                        </View>
                    )}
                    {selectedGatewayName === 'Debit Card' && (
                        <View style={styles.rowSpaceBetween}>
                            <Text>Debit Card:</Text>
                            <Text>₹{totalAmount}</Text>
                        </View>
                    )}
                </>
            )}

            {(discountAmount !== '0' || taxDetails.taxBreakups.length > 0) && <View style={styles.divider} />}

            {discountAmount !== '0' && (
                <View style={styles.rowCenter}>
                    <Text>You saved ₹{discountAmount}</Text>
                </View>
            )}

            {taxDetails.taxBreakups.length > 0 && (
                <>
                    <View style={styles.rowCenter}>
                        <Text style={styles.cell}>CGST</Text>
                        <Text style={styles.cell}>SGST</Text>
                        <Text style={styles.cell}>CGST Amt</Text>
                        <Text style={styles.cell}>SGST Amt</Text>
                    </View>
                    {taxDetails.taxBreakups.map((tax, i) => (
                        <View key={i} style={styles.rowCenter}>
                            <Text style={styles.cell}>{tax.cgstPercent}%</Text>
                            <Text style={styles.cell}>{tax.sgstPercent}%</Text>
                            <Text style={styles.cell}>₹{tax.cgstAmount.toFixed(2)}</Text>
                            <Text style={styles.cell}>₹{tax.sgstAmount.toFixed(2)}</Text>
                        </View>
                    ))}
                </>
            )}

            <Text style={styles.sectionHeader}>Customer Details:</Text>
            <Text>Customer ID: {customerID}</Text>
            <Text>Customer Name: {customerName}</Text>
            <Text>Mobile: {customerPhoneNumber}</Text>
            {customerAddress?.length > 0 && <Text>Address: {customerAddress}</Text>}
            {(customerPlace || customerCity) && <Text>{customerPlace}, {customerCity}</Text>}
            {(customerState || customerPincode) && <Text>{customerState}, {customerPincode}</Text>}

            <View style={styles.divider} />
            <Text style={styles.center}>Thanks & Visit Again!</Text>
        </ScrollView>
    );
};

export default ViewBillScreen;

const styles = StyleSheet.create({
    backButtonContainer: {
        position: 'absolute',
        top: 10,
        left: 10,
        zIndex: 10,
        padding: 8,
    },


    container: { padding: 16 },
    title: { fontSize: 18, fontWeight: 'bold', textAlign: 'center' },
    center: { textAlign: 'center' },
    rowSpaceBetween: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginVertical: 4,
    },
    rowRight: { flexDirection: 'row', justifyContent: 'flex-end' },
    sectionHeader: {
        marginTop: 16,
        fontWeight: 'bold',
        fontSize: 16,
    },
    table: {
        borderWidth: 1,
        marginTop: 8,
    },
    tableRow: {
        flexDirection: 'row',
        borderBottomWidth: 1,
    },
    cell: {
        flex: 1,
        padding: 6,
        textAlign: 'center',
        borderRightWidth: 1,
    },
    bold: { fontWeight: 'bold' },
    rowCenter: {
        flexDirection: 'row',
        justifyContent: 'center',
    },
    divider: {
        marginVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#ccc',
    },
});
