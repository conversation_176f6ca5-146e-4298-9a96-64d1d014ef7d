import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    TextInput,
    Dimensions,
    Modal,
    Platform,
    Alert,
    ActivityIndicator,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import CheckBox from '@react-native-community/checkbox';
import DateTimePicker from '@react-native-community/datetimepicker';
import { DataTable } from 'react-native-paper';
import Navbar from '../../components/Navbar';
import { bearerToken, loginBranchID, loginBranchName, defaultBinId } from '../../globals';
import { useNavigation } from '@react-navigation/native';
import { fetchBusinessDay } from '../../apiHandling/businessDayAPI';
import { fetchBillsByDate } from '../../apiHandling/BillingAPI/fetchBillsAPI';
import { fetchBillDetails } from '../../apiHandling/BillingAPI/fetchBillDetailsAPI';

// api handling

//customer import
import { customerNameSearchAPI } from '../../apiHandling/BillingAPI/customerNameSearchAPI';
import { customerMobileSearchAPI } from '../../apiHandling/BillingAPI/customerMobileSearchAPI';

//item search imports
import { itemCategoryAPI } from '../../apiHandling/BillingAPI/itemCategoryAPI';
import { itemNameAPI } from '../../apiHandling/BillingAPI/itemNameAPI';
import { itemDetailsAPI } from '../../apiHandling/BillingAPI/itemDetailsAPI';
import { itemPriceAPI } from '../../apiHandling/BillingAPI/itemPriceAPI';


const BillingScreen = ({ }) => {


    const [billModalVisible, setBillModalVisible] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
  const [salesList, setSalesList] = useState([]);
  const [filteredSales, setFilteredSales] = useState([]);
  const [selectedDate, setSelectedDate] = useState(null);
  const [showDatePicker, setShowDatePicker] = useState(false);

  // Function to open modal & fetch initial bills & date
  const openBillModal = async () => {
    setLoading(true);
    try {
      const businessDate = await fetchBusinessDay(bearerToken, loginBranchID); // format: DD-MM-YYYY
      const [day, month, year] = businessDate.split('-');
      const apiDate = `${year}${month}${day}`;
      setSelectedDate(new Date(`${year}-${month}-${day}`));

      const bills = await fetchBillsByDate(loginBranchID, 'SALE', selectedBusinessChannel, apiDate, bearerToken);
      setSalesList(bills);
      setFilteredSales(bills);
      setBillModalVisible(true);
    } catch (err) {
      console.error('Error fetching bills:', err);
    }
    setLoading(false);
  };

  // Fetch bills when date changes
  useEffect(() => {
    const fetchBillsBySelectedDate = async () => {
      if (!selectedDate) return;
      setLoading(true);
      try {
        const day = String(selectedDate.getDate()).padStart(2, '0');
        const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
        const year = selectedDate.getFullYear();

        const apiDate = `${year}${month}${day}`;
        const bills = await fetchBillsByDate(loginBranchID, 'SALE', selectedBusinessChannel, apiDate, bearerToken);
        setSalesList(bills);
        // Reset search on date change
        setSearchQuery('');
        setFilteredSales(bills);
      } catch (err) {
        console.error('Error fetching bills by selected date:', err);
      }
      setLoading(false);
    };

    fetchBillsBySelectedDate();
  }, [selectedDate]);

    const handleBillSelect = async (saleId) => {
        const res = await fetchBillDetails(loginBranchID, saleId, bearerToken);

        if (res?.saleHeader?.length && res?.customerInfo?.length) {
            const saleHeader = res.saleHeader[0];
            const saleDetails = res.saleDetails || [];
            const customerInfo = res.customerInfo[0];

            const deliveryAddress = Array.isArray(res.deliveryAddress) && res.deliveryAddress.length > 0
                ? res.deliveryAddress[0]
                : {};

            const billingDetails = Array.isArray(res.billingDetails) && res.billingDetails.length > 0
                ? res.billingDetails[0]
                : {};

            const billData = {
                saleID: saleHeader.SaleId ?? '',
                currentTimeStamp: saleHeader.BusinessDate ?? '',
                itemTableDetails: saleDetails,

                customerID: customerInfo.CustID ?? '',
                customerName: customerInfo.CustomerName ?? '',
                customerPhoneNumber: customerInfo.Mobile ?? '',

                customerAddress: deliveryAddress.CustomerAddress ?? '',
                customerPlace: deliveryAddress.PlaceName ?? '',
                customerCity: deliveryAddress.CityName ?? '',
                customerState: deliveryAddress.StateName ?? '',
                customerPincode: deliveryAddress.PINCODE ?? '',

                deliveryCharges: saleHeader.DeliveryCharge ?? 0,
                selectedGatewayName: billingDetails.PaymentGatewayName ?? '',
                selectedCustomerType: saleHeader.CustomerType ?? '',

                taxAmount: saleHeader.TaxAmount?.toString() ?? '0',
                discountAmount: saleHeader.DiscountAmount?.toString() ?? '0',
                totalAmount: saleHeader.TotalAmount?.toString() ?? '0',
                roundOffAmount: saleHeader.RoundOffAmount?.toString() ?? '0',

                printDateTime: new Date().toLocaleString(),
            };

            setBillModalVisible(false);
            navigation.navigate('ViewBillScreen', billData);
        } else {
            setBillModalVisible(false);
            // Optional: handle error or show toast
        }
    };




    const navigation = useNavigation();
    // new, save, cancel
    const [selectedScrollOption, setSelectedScrollOption] = useState(null);

    // retail, horeca & pos, web
    const [selectedSaleType, setSelectedSaleType] = useState('RETAIL');
    const [selectedBusinessChannel, setSelectedBusinessChannel] = useState('POS');


    // customer section
    const [customerDialogVisible, setCustomerDialogVisible] = useState(false);
    const [customerList, setCustomerList] = useState([]);
    const [loading, setLoading] = useState(false);

    const [customerId, setCustomerId] = useState('');
    const [customerName, setCustomerName] = useState('');
    const [customerMobile, setCustomerMobile] = useState('');

    const fetchCustomers = async (customerMobile) => {
        setLoading(true);
        setCustomerList([]);
        let result;

        if (customerMobile.length === 10) {
            result = await customerMobileSearchAPI(loginBranchID, customerMobile, bearerToken);
        } else {
            result = await customerNameSearchAPI(loginBranchID, customerName, bearerToken);
        }

        if (result.success) {
            setCustomerList(result.customers);
            setCustomerDialogVisible(true);
        } else {
            alert(result.message);
        }

        setLoading(false);
    };

    const handleCustomerSelect = (customer) => {
        setCustomerDialogVisible(false);
        setCustomerId(customer.customerCode);
        setCustomerName(customer.customerName);
        setCustomerMobile(customer.mobile);
    };


    // item search

    const [selectedItemCategory, setItemCategory] = useState('Category');
    const [selectedItemCategoryId, setItemCategoryId] = useState('');
    const [selectedItemName, setItemName] = useState('Item Type');
    const [selectedItemId, setItemId] = useState('');
    const [selectedItemBatchNumber, setBatchNumber] = useState('');
    const [stockQty, setStockQty] = useState('');

    const [itemCategoryDialogVisible, setItemCategoryDialogVisible] = useState(false);
    const [itemNameDialogVisible, setItemNameDialogVisible] = useState(false);
    const [itemCategoryList, setItemCategoryList] = useState([]);
    const [itemNameList, setItemNameList] = useState([]);

    const fetchItemCategories = async () => {
        setLoading(true);
        let result;
        resetItemSelection();
        result = await itemCategoryAPI(loginBranchID, bearerToken);
        if (result.success) {
            setItemCategoryList(result.categories);
            setItemCategoryDialogVisible(true);
        }
        else {
            alert(result.message);
        }
        setLoading(false);
    }

    const handleCategorySelect = (itemCategory) => {
        setItemCategoryDialogVisible(false);
        setItemCategory(itemCategory.CategoryName);
        setItemCategoryId(itemCategory.ItemCategoryID);
        fetchItemNames(itemCategory.ItemCategoryID);
    }

    const fetchItemNames = async (selectedItemCategoryId) => {
        setLoading(true);
        let result;
        result = await itemNameAPI(loginBranchID, defaultBinId, selectedItemCategoryId, bearerToken);
        if (result.success) {
            setItemNameList(result.itemNames);
            setItemNameDialogVisible(true);
        }
        else {
            alert(result.message);
        }
        setLoading(false);
    }

    const handleNameSelect = (itemNames) => {
        setItemNameDialogVisible(false);
        setItemName(itemNames.ItemName);
        setItemId(itemNames.ItemID);
        setStockQty(itemNames.StockQty);
        setBatchNumber(itemNames.BatchNumber);

        fetchItemDetails(itemNames.ItemID);
        fetchItemPrice(itemNames.ItemID);

    }
    useEffect(() => {
        if (selectedItemBatchNumber && selectedItemName && selectedItemId && stockQty) {
            console.log(selectedItemBatchNumber, selectedItemCategory, selectedItemCategoryId, selectedItemName, selectedItemId, stockQty);
        }
    }, [selectedItemBatchNumber, selectedItemName, selectedItemId, stockQty]);

    const [itemDetailsList, setItemDetailsList] = useState([]);

    const [stockGroupId, setStockGroupId] = useState('');
    const [taxCategoryId, setTaxCategoryId] = useState('');
    const [batchEnabled, setBatchEnabled] = useState(false);
    const [sellByWeight, setSellByWeight] = useState(false);
    const [altQtyEnabled, setAltQtyEnabled] = useState(false);
    const [totalTaxPercent, setTotalTaxPercent] = useState(0);


    const fetchItemDetails = async (selectedItemId) => {
        let result;
        result = await itemDetailsAPI(selectedItemId, bearerToken);
        if (result.success) {
            const details = result.itemDetails;
            setItemDetailsList(details);
            setStockGroupId(details.StockGroupId || '');

            setTaxCategoryId(details.TaxCategoryId || '');

            setBatchEnabled(!!details.BatchEnabled);

            setSellByWeight(!!details.SellByWeight);

            setAltQtyEnabled(!!details.AltQtyEnabled);

            setTotalTaxPercent(details.TotalTaxPercent || 0);

        } else {
            alert(result.message);
        }
    }

    const [itemDayRate, setItemDayRate] = useState('0');
    const [itemPrice, setItemPrice] = useState('');

    const fetchItemPrice = async (selectedItemId) => {
        const result = await itemPriceAPI(loginBranchID, selectedItemId, bearerToken);

        if (result && typeof result === 'number') {
            setItemDayRate(result.toString());
            setItemPrice(result.toString());
        } else if (result?.success === false) {
            console.error('Failed to fetch item price:', result.message);
            setItemDayRate('0');
            setItemPrice('');
        }
    };

    const resetItemSelection = () => {
        setItemCategory('Category');
        setItemCategoryId('');
        setItemName('Item Type');
        setItemId('');
        setBatchNumber('');
        setStockQty('');
        setNos('');
        setWeight('');
        setItemPrice('');
        setItemDayRate('0');
        setPriceWarning('');
        setNosError('');
        setWeightError('');
        setItemDetailsList([]);
        setStockGroupId('');
        setTaxCategoryId('');
        setBatchEnabled(false);
        setSellByWeight(false);
        setAltQtyEnabled(false);
        setTotalTaxPercent(0);
        setItemCategoryDialogVisible(false);
        setItemNameDialogVisible(false);
        setItemCategoryList([]);
        setItemNameList([]);
    };


    const [nos, setNos] = useState('');
    const [weight, setWeight] = useState('');
    const [priceWarning, setPriceWarning] = useState('');
    const [nosError, setNosError] = useState('');
    const [weightError, setWeightError] = useState('');

    const totalPrice = (() => {
        const priceNum = parseFloat(itemPrice) || 0;
        const weightNum = parseFloat(weight) || 0;
        const nosNum = parseFloat(nos) || 0;

        let total = 0;

        if (weightNum > 0) {
            total = priceNum * weightNum;
        } else if (nosNum > 0) {
            total = priceNum * nosNum;
        }

        return total.toFixed(2);
    })();

    const [itemTableDetails, setItemTableDetails] = useState([]);


    const addItemToTable = () => {
        const weightVal = parseFloat(weight) || 0;
        const nosVal = parseFloat(nos) || 0;
        const priceVal = parseFloat(itemPrice) || 0;

        // Calculate base total price (without tax/discount)
        const baseTotalPrice = weightVal > 0 ? priceVal * weightVal : priceVal * nosVal;
        const taxAmount = (baseTotalPrice * totalTaxPercent) / 100;
        const finalTotalPrice = baseTotalPrice + taxAmount;

        const newItem = {
            itemId: selectedItemId,
            itemName: selectedItemName,
            itemCategory: selectedItemCategory,
            itemCategoryId: selectedItemCategoryId,
            batchNumber: selectedItemBatchNumber || '',
            price: priceVal,
            itemDayRate: parseFloat(itemDayRate),
            weight: weightVal,
            quantity: nosVal,
            stockQty: parseFloat(stockQty),
            totalPrice: baseTotalPrice.toFixed(2), // Price * Qty/Weight
            taxPercent: totalTaxPercent || 0,
            taxAmount: taxAmount.toFixed(2),
            finalTotalPrice: finalTotalPrice.toFixed(2), // Base + Tax
            discount: 0,

            // Additional Details
            stockGroupId: stockGroupId || '',
            taxCategoryId: taxCategoryId || '',
            batchEnabled: batchEnabled,
            sellByWeight: sellByWeight,
            altQtyEnabled: altQtyEnabled,
        };

        setItemTableDetails((prev) => [...prev, newItem]);
        resetItemSelection();
    };


    const summary = itemTableDetails.reduce((acc, item) => {
        acc.kg += item.weight;
        acc.nos += item.quantity;
        acc.amount += parseFloat(item.totalPrice);
        acc.tax += parseFloat(item.taxAmount);
        acc.discount += item.discount || 0;
        return acc;
    }, { kg: 0, nos: 0, amount: 0, tax: 0, discount: 0 });

    const total = Math.round(summary.amount + summary.tax - summary.discount);







    return (
        <View style={styles.container}>
            {/* App Bar */}
            <Navbar />

            {/* Scroll Options */}
            <View style={styles.scrollOptions_container}>
                <View style={styles.scrollOptions_row}>
                    {['New', 'Save', 'View', 'Cancel Bill'].map((option, index) => {
                        const isScrollButtonSelected = selectedScrollOption === option;

                        let buttonStyle = [styles.scrollOptions_button];

                        if (option === 'Cancel Bill') {
                            buttonStyle.push({
                                backgroundColor: isScrollButtonSelected ? '#FE0000' : '#FF3333',
                            });
                        } else if (option === 'Save') {
                            buttonStyle.push({
                                backgroundColor: isScrollButtonSelected ? '#02720F' : '#02A515',
                            });
                        } else {
                            buttonStyle.push({
                                backgroundColor: isScrollButtonSelected ? '#02096A' : '#DEDDDD',
                            });
                        }

                        return (
                            <View style={styles.scrollOptions_buttonWrapper} key={index}>
                                <TouchableOpacity
                                    style={buttonStyle}
                                    onPress={async () => {
                                        setSelectedScrollOption(option);

                                        if (option === 'Save') {
                                            navigation.navigate('BillingPayment', {
                                                customerId,
                                                customerName,
                                                customerMobile,
                                                selectedSaleType,
                                                selectedBusinessChannel,
                                                itemTableDetails,
                                                summary,
                                                total,
                                            });
                                        } else if (option === 'View') {
                                            await openBillModal(); // trigger the popup
                                        }
                                    }}
                                >
                                    <Text style={[
                                        styles.scrollOptions_buttonText,
                                        { color: isScrollButtonSelected ? 'white' : 'black' },
                                    ]}>
                                        {option}
                                    </Text>
                                </TouchableOpacity>

                            </View>
                        );
                    })}
                </View>
            </View>

            <Modal
                animationType="slide"
                transparent={true}
                visible={billModalVisible}
                onRequestClose={() => setBillModalVisible(false)}
            >
                <View
                    style={{
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: 'rgba(0,0,0,0.5)',
                    }}
                >
                    <View
                        style={{
                            backgroundColor: '#fff',
                            borderRadius: 10,
                            padding: 20,
                            width: '90%',
                            maxHeight: '80%',
                        }}
                    >
                        {/* Search & Date Picker Row */}
                        <View
                            style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}
                        >
                            <TextInput
                                placeholder="Search..."
                                value={searchQuery}
                                onChangeText={(text) => {
                                    setSearchQuery(text);
                                    const filtered = salesList.filter((sale) =>
                                        (sale.DocName || '').toLowerCase().includes(text.toLowerCase())
                                    );
                                    setFilteredSales(filtered);
                                }}
                                style={{
                                    flex: 1,
                                    borderWidth: 1,
                                    borderColor: '#ccc',
                                    borderRadius: 8,
                                    paddingHorizontal: 10,
                                    height: 40,
                                    marginRight: 8,
                                }}
                            />
                            <TouchableOpacity
                                onPress={() => setShowDatePicker(true)}
                                style={{
                                    borderWidth: 1,
                                    borderColor: '#ccc',
                                    borderRadius: 8,
                                    paddingHorizontal: 10,
                                    paddingVertical: Platform.OS === 'ios' ? 8 : 6,
                                    height: 40,
                                    justifyContent: 'center',
                                }}
                            >
                                <Text>{selectedDate ? selectedDate.toLocaleDateString() : 'Pick Date'}</Text>
                            </TouchableOpacity>
                        </View>

                        {/* DateTimePicker */}
                        {showDatePicker && (
                            <DateTimePicker
                                value={selectedDate || new Date()}
                                mode="date"
                                display="default"
                                onChange={(event, date) => {
                                    setShowDatePicker(false);
                                    if (date) setSelectedDate(date);
                                }}
                            />
                        )}

                        {/* Bills List */}
                        <ScrollView
                            contentContainerStyle={{
                                flexDirection: 'row',
                                flexWrap: 'wrap',
                                gap: 10,
                            }}
                        >
                            {filteredSales.map((sale, index) => (
                                <TouchableOpacity
                                    key={index}
                                    onPress={() => {
                                        handleBillSelect(sale.SaleId);
                                        setBillModalVisible(false);
                                    }}
                                    style={{
                                        backgroundColor: '#FDC500',
                                        padding: 10,
                                        borderRadius: 8,
                                        minWidth: '22%',
                                        margin: 5,
                                        alignItems: 'center',
                                    }}
                                >
                                    <Text style={{ color: '#000', textAlign: 'center' }}>{sale.DocName}</Text>
                                </TouchableOpacity>
                            ))}
                        </ScrollView>

                        {/* Back Button */}
                        <TouchableOpacity onPress={() => setBillModalVisible(false)}>
                            <Text style={{ marginTop: 20, textAlign: 'center', color: '#000' }}>Back</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>


            <View style={styles.saleBusinessContainer}>
                {/* Sale Type */}
                <View style={styles.saleType_container}>
                    <View style={styles.saleType_row}>
                        {['RETAIL', 'HORECA'].map((type) => (
                            <TouchableOpacity
                                key={type}
                                onPress={() => setSelectedSaleType(type)}
                                style={[
                                    styles.saleType_button,
                                    {
                                        backgroundColor:
                                            selectedSaleType == type ? '#02096A' : '#D3D3D3',
                                        flex: 1,
                                    },
                                ]}
                            >
                                <Text
                                    style={[
                                        styles.saleType_text,
                                        {
                                            color: selectedSaleType == type ? 'white' : 'black',
                                        },
                                    ]}
                                >
                                    {type}
                                </Text>
                            </TouchableOpacity>
                        ))}
                    </View>
                </View>

                {/* Business Channel */}
                <View style={styles.businessChannel_container}>
                    <View style={styles.businessChannel_row}>
                        {['POS', 'WEB'].map((button) => (
                            <TouchableOpacity
                                key={button}
                                onPress={() => setSelectedBusinessChannel(button)}
                                style={[
                                    styles.businessChannel_button,
                                    {
                                        backgroundColor:
                                            selectedBusinessChannel == button ? '#02096A' : '#D3D3D3',
                                        flex: 1,
                                    },
                                ]}
                            >
                                <Text
                                    style={[
                                        styles.businessChannel_text,
                                        {
                                            color:
                                                selectedBusinessChannel == button ? 'white' : 'black',
                                        },
                                    ]}
                                >
                                    {button}
                                </Text>
                            </TouchableOpacity>
                        ))}
                    </View>
                </View>
            </View>


            {/* Customer Details */}
            <View style={styles.customer_container}>
                {/* Row 1: Customer ID, Phone, Name */}
                <View style={styles.customer_row}>
                    <View style={styles.customer_phoneField}>
                        <TextInput
                            placeholder="Customer ID"
                            style={[
                                styles.customer_textfield,
                                { color: '#888', backgroundColor: '#f0f0f0' }
                            ]}
                            value={customerId}
                            onChangeText={setCustomerId}
                            editable={false}
                        />
                    </View>

                    <View style={styles.customer_phoneField}>
                        <TextInput
                            placeholder="Phone"
                            keyboardType="numeric"
                            style={styles.customer_textfield}
                            value={customerMobile}
                            onChangeText={(text) => {
                                setCustomerMobile(text);
                                if (text.length === 10) {
                                    fetchCustomers(text);
                                }
                            }}
                            maxLength={10}
                        />
                    </View>

                    <View style={styles.customer_nameField}>
                        <TextInput
                            placeholder="Name"
                            style={styles.customer_textfield}
                            value={customerName}
                            onChangeText={setCustomerName}
                        />
                    </View>

                    <TouchableOpacity style={styles.customer_button}>
                        <Text style={styles.customer_buttonText}>Edit</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={styles.customer_button}
                        onPress={() => fetchCustomers(customerMobile)}
                    >
                        <Text style={styles.customer_buttonText}>Search</Text>
                    </TouchableOpacity>
                </View>
            </View>

            {/* customer search pop up */}

            <Modal visible={customerDialogVisible} transparent={true} animationType="slide">
                <View style={{
                    flex: 1,
                    justifyContent: 'center',
                    backgroundColor: 'rgba(0,0,0,0.5)',
                }}>
                    <View style={{
                        backgroundColor: 'white',
                        marginHorizontal: 20,
                        borderRadius: 12,
                        padding: 20,
                        maxHeight: '80%',
                    }}>
                        <Text style={{
                            fontSize: 18,
                            fontWeight: 'bold',
                            marginBottom: 10,
                            textAlign: 'center',
                        }}>Select a Customer</Text>

                        <ScrollView contentContainerStyle={{
                            flexDirection: 'row',
                            flexWrap: 'wrap',
                            justifyContent: 'space-between',
                            paddingVertical: 10,
                        }}>
                            {customerList.length === 0 ? (
                                <Text style={{
                                    width: '100%',
                                    textAlign: 'center',
                                    fontSize: 14,
                                    color: 'gray',
                                    marginTop: 20,
                                }}>
                                    No customer found.
                                </Text>
                            ) : (
                                customerList.map((cust, index) => (
                                    <TouchableOpacity
                                        key={index}
                                        style={{
                                            width: '30%',
                                            backgroundColor: '#EFEFEF',
                                            padding: 10,
                                            marginVertical: 6,
                                            borderRadius: 6,
                                            alignItems: 'center',
                                        }}
                                        onPress={() => handleCustomerSelect(cust)}
                                    >
                                        <Text style={{
                                            textAlign: 'center',
                                            fontSize: 12,
                                        }}>{cust.customerName}</Text>
                                    </TouchableOpacity>
                                ))
                            )}
                        </ScrollView>

                        <TouchableOpacity
                            onPress={() => setCustomerDialogVisible(false)}
                            style={{
                                marginTop: 10,
                                backgroundColor: '#FF3B30',
                                padding: 10,
                                borderRadius: 8,
                                alignSelf: 'center',
                            }}
                        >
                            <Text style={{
                                color: 'white',
                                fontWeight: 'bold',
                            }}>Cancel</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>

            {loading && (
                <View style={{
                    position: 'absolute',
                    top: 0,
                    bottom: 0,
                    right: 0,
                    left: 0,
                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: '#ffffffcc',
                }}>
                    <ActivityIndicator size="large" color="#0000ff" />
                </View>
            )}

            {/* item search */}
            <Modal visible={itemCategoryDialogVisible} transparent={true} animationType="slide">
                <View style={{
                    flex: 1,
                    justifyContent: 'center',
                    backgroundColor: 'rgba(0,0,0,0.5)',
                }}>
                    <View style={{
                        backgroundColor: 'white',
                        marginHorizontal: 20,
                        borderRadius: 12,
                        padding: 20,
                        maxHeight: '80%',
                    }}>
                        <Text style={{
                            fontSize: 18,
                            fontWeight: 'bold',
                            marginBottom: 10,
                            textAlign: 'center',
                        }}>Select a category</Text>

                        <ScrollView contentContainerStyle={{
                            flexDirection: 'row',
                            flexWrap: 'wrap',
                            justifyContent: 'space-between',
                            paddingVertical: 10,
                        }}>
                            {itemCategoryList.length === 0 ? (
                                <Text style={{
                                    width: '100%',
                                    textAlign: 'center',
                                    fontSize: 14,
                                    color: 'gray',
                                    marginTop: 20,
                                }}>
                                    No category found.
                                </Text>
                            ) : (
                                itemCategoryList.map((itemCategory, index) => (
                                    <TouchableOpacity
                                        key={index}
                                        style={{
                                            width: '30%',
                                            backgroundColor: '#EFEFEF',
                                            padding: 10,
                                            marginVertical: 6,
                                            borderRadius: 6,
                                            alignItems: 'center',
                                        }}
                                        onPress={() => handleCategorySelect(itemCategory)}
                                    >
                                        <Text style={{
                                            textAlign: 'center',
                                            fontSize: 12,
                                        }}>{itemCategory.CategoryName}</Text>
                                    </TouchableOpacity>
                                ))
                            )}
                        </ScrollView>

                        <TouchableOpacity
                            onPress={() => setItemCategoryDialogVisible(false)}
                            style={{
                                marginTop: 10,
                                backgroundColor: '#FF3B30',
                                padding: 10,
                                borderRadius: 8,
                                alignSelf: 'center',
                            }}
                        >
                            <Text style={{
                                color: 'white',
                                fontWeight: 'bold',
                            }}>Cancel</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>


            <Modal visible={itemNameDialogVisible} transparent={true} animationType="slide">
                <View style={{
                    flex: 1,
                    justifyContent: 'center',
                    backgroundColor: 'rgba(0,0,0,0.5)',
                }}>
                    <View style={{
                        backgroundColor: 'white',
                        marginHorizontal: 20,
                        borderRadius: 12,
                        padding: 20,
                        maxHeight: '80%',
                    }}>
                        <Text style={{
                            fontSize: 18,
                            fontWeight: 'bold',
                            marginBottom: 10,
                            textAlign: 'center',
                        }}>Select a item type</Text>

                        <ScrollView contentContainerStyle={{
                            flexDirection: 'row',
                            flexWrap: 'wrap',
                            justifyContent: 'space-between',
                            paddingVertical: 10,
                        }}>
                            {itemNameList.length === 0 ? (
                                <Text style={{
                                    width: '100%',
                                    textAlign: 'center',
                                    fontSize: 14,
                                    color: 'gray',
                                    marginTop: 20,
                                }}>
                                    No stock available
                                </Text>
                            ) : (
                                itemNameList.map((itemNames, index) => (
                                    <TouchableOpacity
                                        key={index}
                                        style={{
                                            width: '30%',
                                            backgroundColor: '#EFEFEF',
                                            padding: 10,
                                            marginVertical: 6,
                                            borderRadius: 6,
                                            alignItems: 'center',
                                        }}
                                        onPress={() => handleNameSelect(itemNames)}
                                    >
                                        <Text style={{
                                            textAlign: 'center',
                                            fontSize: 12,
                                        }}>{itemNames.BatchNumber} {itemNames.ItemName}</Text>
                                    </TouchableOpacity>
                                ))
                            )}
                        </ScrollView>

                        <TouchableOpacity
                            onPress={() => {
                                setItemNameDialogVisible(false);
                                fetchItemCategories();
                            }}
                            style={{
                                marginTop: 10,
                                backgroundColor: '#FF3B30',
                                padding: 10,
                                borderRadius: 8,
                                alignSelf: 'center',
                            }}
                        >
                            <Text style={{
                                color: 'white',
                                fontWeight: 'bold',
                            }}>Cancel</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </Modal>


            {/* Item Details */}
            <View style={styles.item_container}>

                {/* Row 1: Category and Item Type */}
                <View style={styles.item_row1}>
                    <View style={styles.item_chooseBox}>
                        <TouchableOpacity style={styles.item_chooseButton} onPress={() => fetchItemCategories()}>

                            <Text style={styles.item_actionButtonText}>Choose Category</Text>
                        </TouchableOpacity>
                    </View>

                    <Text style={styles.item_titleText}>
                        {selectedItemCategory + '/ ' + selectedItemName}
                    </Text>
                </View>


                {/* Row 2: Batch No, Nos, Weight */}
                <View style={styles.item_row2}>
                    <View style={{ flex: 1, marginRight: 8 }}>
                        <TextInput
                            style={[
                                styles.item_input,
                                { width: '100%', backgroundColor: batchEnabled ? '#f0f0f0' : '#e0e0e0' }
                            ]}
                            placeholder="Batch No."
                            keyboardType="numeric"
                            value={selectedItemBatchNumber}
                            onChangeText={() => { }}
                            editable={false}
                        />

                    </View>
                    <View style={{ flex: 2, marginHorizontal: 8 }}>
                        <TextInput
                            style={[
                                styles.item_input,
                                { width: '100%', backgroundColor: (altQtyEnabled || !sellByWeight) ? '#ffffff' : '#e0e0e0' }
                            ]}
                            placeholder="Nos"
                            keyboardType="numeric"
                            value={nos}
                            onChangeText={(text) => {
                                const valid = /^\d*\.?\d{0,2}$/.test(text);
                                if (valid) {
                                    setNos(text); // Always allow updating the field if valid number format
                                    if (text === '') {
                                        setNosError('');
                                    } else if (parseFloat(text) > parseFloat(stockQty)) {
                                        setNosError(`Exceeds stock quantity (${stockQty})`);
                                    } else {
                                        setNosError('');
                                    }
                                }
                            }}

                            editable={altQtyEnabled || !sellByWeight}
                        />
                        {nosError ? (
                            <Text style={{ color: 'red', fontSize: 12 }}>{nosError}</Text>
                        ) : null}
                    </View>

                    <View style={{ flex: 2, marginLeft: 8 }}>
                        <TextInput
                            style={[
                                styles.item_input,
                                { width: '100%', backgroundColor: (altQtyEnabled || sellByWeight) ? '#ffffff' : '#e0e0e0' }
                            ]}
                            placeholder="Weight (kg)"
                            keyboardType="numeric"
                            value={weight}
                            onChangeText={(text) => {
                                const valid = /^\d*\.?\d{0,3}$/.test(text);
                                if (text === '' || valid) {
                                    setWeight(text); // Allow empty or valid input
                                    if (text === '') {
                                        setWeightError('');
                                    } else if (parseFloat(text) > parseFloat(stockQty)) {
                                        setWeightError(`Exceeds stock quantity (${stockQty})`);
                                    } else {
                                        setWeightError('');
                                    }
                                }
                            }}

                            editable={altQtyEnabled || sellByWeight}
                        />
                        {weightError ? (
                            <Text style={{ color: 'red', fontSize: 12 }}>{weightError}</Text>
                        ) : null}
                    </View>

                </View>

                {/* Row 3: Discount Dropdown, Price per Unit, Discount % */}
                <View style={styles.item_row3}>
                    <View style={{ flex: 1, marginRight: 8 }}>
                        <Dropdown
                            data={[
                                { label: 'Discount on', value: 'Discount on' },
                                { label: 'Total value', value: 'Total value' },
                                { label: 'Total kg', value: 'Total kg' },
                                { label: 'Total percentage', value: 'Total percentage' },
                            ]}
                            labelField="label"
                            valueField="value"
                            placeholder="Discount"
                            editable={false}
                            disabled={true}
                            value=''
                            onChange={() => { }}
                            style={styles.item_dropdown}
                        />
                    </View>
                    <View style={{ flex: 1, marginHorizontal: 8 }}>
                        <TextInput
                            style={[styles.item_input, { width: '100%' }]}
                            placeholder="Price per Unit"
                            keyboardType="decimal-pad"
                            value={itemPrice}
                            onChangeText={(text) => {
                                const valid = /^\d*\.?\d{0,2}$/.test(text);
                                if (text === '' || valid) {
                                    setItemPrice(text); // Allow empty or valid input
                                    if (text === '') {
                                        setPriceWarning('');
                                    } else if (parseFloat(text) < parseFloat(itemDayRate)) {
                                        setPriceWarning(`Less than ₹${itemDayRate}`);
                                    } else {
                                        setPriceWarning('');
                                    }
                                }
                            }}

                        />
                        {priceWarning ? (
                            <Text style={{ color: 'red', fontSize: 12 }}>{priceWarning}</Text>
                        ) : null}

                    </View>
                    <View style={{ flex: 1, marginLeft: 8 }}>
                        <TextInput
                            style={[styles.item_input, { width: '100%' }]}
                            placeholder="Discount %"
                            editable={false}
                            keyboardType="decimal-pad"
                        />
                    </View>
                </View>

                {/* Row 4: Remarks */}
                <View style={styles.item_row4}>
                    <View style={{ flex: 1 }}>
                        <TextInput
                            style={[styles.item_input, { width: '100%' }]}
                            placeholder="Remarks"
                        />
                    </View>
                </View>

                {/* Row 5: Total Price and Buttons */}
                <View style={styles.item_row5}>
                    <View style={[styles.item_totalPriceContainer, { flex: 1 }]}>
                        <Text style={styles.item_totalPriceText}>
                            Total Price: ₹{totalPrice}
                        </Text>
                    </View>


                    <TouchableOpacity style={[styles.item_actionButton, { flex: 1 }]}
                        onPress={() => { addItemToTable(); }}>
                        <Text style={styles.item_actionButtonText}>Add</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[styles.item_actionButton, { flex: 1 }]}
                        onPress={() => {
                            resetItemSelection();

                        }}
                    >
                        <Text style={styles.item_actionButtonText}>Clear</Text>
                    </TouchableOpacity>
                </View>

            </View>


            {/* Table */}
            <View style={styles.table_container}>
                <ScrollView horizontal>
                    <View style={{ width: Dimensions.get('window').width }}>
                        <ScrollView>
                            <DataTable>
                                <DataTable.Header style={styles.table_header}>
                                    <DataTable.Title><Text style={styles.table_headerText}>Select</Text></DataTable.Title>
                                    <DataTable.Title><Text style={styles.table_headerText}>BatchNo</Text></DataTable.Title>
                                    <DataTable.Title><Text style={styles.table_headerText}>Item Name</Text></DataTable.Title>
                                    <DataTable.Title><Text style={styles.table_headerText}>Price</Text></DataTable.Title>
                                    <DataTable.Title><Text style={styles.table_headerText}>Weight (kg)</Text></DataTable.Title>
                                    <DataTable.Title><Text style={styles.table_headerText}>Quantity</Text></DataTable.Title>
                                    <DataTable.Title><Text style={styles.table_headerText}>Total Price</Text></DataTable.Title>
                                </DataTable.Header>

                                {/* Replace this block with dynamic rows later */}
                                {itemTableDetails.map((item, index) => (
                                    <DataTable.Row key={index}>
                                        <DataTable.Cell>□</DataTable.Cell>
                                        <DataTable.Cell>{item.batchNumber}</DataTable.Cell>
                                        <DataTable.Cell>{item.itemName}</DataTable.Cell>
                                        <DataTable.Cell>₹{item.price.toFixed(2)}</DataTable.Cell>
                                        <DataTable.Cell>{item.weight.toFixed(3)}</DataTable.Cell>
                                        <DataTable.Cell>{item.quantity.toFixed(2)}</DataTable.Cell>
                                        <DataTable.Cell>₹{item.totalPrice}</DataTable.Cell>
                                    </DataTable.Row>
                                ))}

                            </DataTable>
                        </ScrollView>
                    </View>
                </ScrollView>
            </View>

            <View style={styles.summary_row}>
                <View style={styles.summary_box}>
                    <View style={styles.summary_detailRow}>
                        <Text style={styles.summary_label}>Kg:</Text>
                        <View style={styles.summary_valueBox}>
                            <Text style={styles.summary_valueText}>{summary.kg.toFixed(3)}</Text>
                        </View>

                        <Text style={styles.summary_label}>Nos:</Text>
                        <View style={styles.summary_valueBox}>
                            <Text style={styles.summary_valueText}>{summary.nos.toFixed(2)}</Text>
                        </View>

                        <Text style={styles.summary_label}>Amount:</Text>
                        <View style={styles.summary_valueBox}>
                            <Text style={styles.summary_valueText}>₹{summary.amount.toFixed(2)}</Text>
                        </View>
                    </View>

                    <View style={styles.summary_detailRow}>
                        <Text style={styles.summary_label}>Tax:</Text>
                        <View style={styles.summary_valueBox}>
                            <Text style={styles.summary_valueText}>₹{summary.tax.toFixed(2)}</Text>
                        </View>

                        <Text style={styles.summary_label}>Discount:</Text>
                        <View style={styles.summary_valueBox}>
                            <Text style={styles.summary_valueText}>₹{summary.discount.toFixed(2)}</Text>
                        </View>

                        <Text style={styles.summary_label}>Total:</Text>
                        <View style={styles.summary_valueBox}>
                            <Text style={styles.summary_valueText}>₹{total.toFixed(2)}</Text>
                        </View>
                    </View>
                </View>

                <TouchableOpacity style={styles.summary_deleteButton}>
                    <Text style={{ color: 'white' }}>Delete</Text>
                </TouchableOpacity>
            </View>

        </View>
    );


};

const styles = StyleSheet.create({
    // ==================== COMMON STYLES ====================
    container: {
        flex: 1,
    },

    // ==================== DIALOG STYLES ====================
    dialog_overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    dialog_container: {
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 16,
        width: '80%',
        maxHeight: '80%',
    },
    dialog_title: {
        fontSize: 15,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 20,
    },
    dialog_content: {
        maxHeight: Dimensions.get('window').height * 0.6,
    },
    dialog_grid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'flex-start', // Changed from space-between to flex-start
        gap: 10,
    },
    dialog_button: {
        backgroundColor: '#FDC500',
        borderRadius: 8,
        padding: 10,
        width: '23%',
        minHeight: 100, // Set minimum height instead of aspect ratio
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 10,
        marginRight: '2%', // Add margin to ensure proper spacing
    },
    dialog_buttonText: {
        fontSize: 14,
        color: 'rgb(2, 2, 2)',
        textAlign: 'center',
    },
    dialog_batchText: {
        fontSize: 12,
        color: '#02096A',
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 4,
    },
    dialog_backButton: {
        marginTop: 18,
        alignSelf: 'center',
    },
    dialog_backButtonText: {
        color: 'black',
        fontSize: 15,
    },
    // Loading styles
    loadingContainer: {
        padding: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    loadingText: {
        marginTop: 10,
        fontSize: 14,
        color: '#333',
    },
    fullScreenLoading: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },

    // ==================== APP BAR STYLES ====================
    appBar_container: {
        backgroundColor: '#02096A',
        paddingTop: 10,
        paddingBottom: 10,
    },
    appBar_branchRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        marginBottom: 5,
    },
    appBar_branchText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },

    // ==================== MENU BUTTON STYLES ====================  appbar
    menu_row: {
        paddingHorizontal: 8,
        alignItems: 'center',
        gap: 8,
    },
    menu_button: {
        paddingHorizontal: 12,
        paddingVertical: 8,
        marginHorizontal: 4,
        borderRadius: 5,
    },
    menu_text: {
        fontSize: 14,
        fontWeight: 'bold',
    },

    // ==================== PAGE CONTENT STYLES ====================
    pageContent: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#E6E6E6',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
    },

    // ==================== SCROLL OPTIONS STYLES ====================   new
    scrollOptions_container: {
        backgroundColor: '#E6E6E6',
        paddingVertical: 8,
        marginTop: 0, // Ensure no margin at the top
    },
    scrollOptions_row: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        flexWrap: 'wrap',
        paddingHorizontal: 8,
        gap: 8,
    },
    //NEW
    scrollOptions_buttonWrapper: {
        flex: 1,
        marginHorizontal: 4,
    },
    scrollOptions_button: {
        height: 60,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 10,
        borderWidth: 1.5,
        borderColor: 'transparent',
    },
    scrollOptions_buttonText: {
        fontSize: 18,
        fontWeight: 'bold',
    },

    // ==================== SALE TYPES & BUSINESS CHANNELS CONTAINER ====================
    saleBusinessContainer: {
        flexDirection: 'row',
        backgroundColor: '#E6E6E6',
        padding: 8,
    },

    // ==================== SALE TYPES STYLES ====================   retail / horeca
    saleType_container: {
        flex: 1,
        paddingLeft: 4,
    },
    saleType_row: {
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        backgroundColor: '#F0F0F0',
        borderWidth: 1,
        borderColor: '#041C44',
        borderRadius: 10,
        padding: 5,
    },
    //RETAIL/HORIKA
    saleType_button: {
        marginHorizontal: 5,
        paddingVertical: 12,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center',
    },
    saleType_text: {
        fontSize: 24,
        fontWeight: 'bold',
        fontFamily: 'Poppins',
        letterSpacing: 2,
    },

    // ==================== BUSINESS CHANNEL STYLES ====================  pos/web
    businessChannel_container: {
        flex: 1,
        paddingRight: 4,
        paddingHorizontal: 8,
    },
    businessChannel_row: {
        flexDirection: 'row',
        justifyContent: 'space-evenly',
        alignItems: 'center',
        backgroundColor: '#F0F0F0',
        borderWidth: 1,
        borderColor: '#041C44',
        borderRadius: 10,
        padding: 5,
    },
    //POS/WEB
    businessChannel_button: {
        marginHorizontal: 5,
        paddingVertical: 12,
        borderRadius: 10,
        alignItems: 'center',
        justifyContent: 'center',
    },
    businessChannel_text: {
        fontSize: 24,
        fontWeight: 'bold',
        fontFamily: 'Poppins',
        letterSpacing: 2,
    },

    // ==================== CUSTOMER DETAILS STYLES ====================
    customer_container: {
        padding: 8,
        width: '100%',
    },
    customer_row: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 10,
    },
    customer_phoneField: {
        minWidth: 120,
        height: 55,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        justifyContent: 'center',
        paddingHorizontal: 12,
    },
    customer_nameField: {
        flex: 1,
        height: 55,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        justifyContent: 'center',
        paddingHorizontal: 12,
        marginHorizontal: 8,
    },
    customer_textfield: {
        fontSize: 14,
        fontWeight: '500',
        flexShrink: 1,
        flexGrow: 1,
        width: '100%',
    },
    customer_button: {
        backgroundColor: '#02096A',
        paddingVertical: 14,
        paddingHorizontal: 16,
        borderRadius: 10,
        justifyContent: 'center',
        alignItems: 'center',
        marginHorizontal: 4,
    },
    customer_buttonText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,
    },

    // ==================== ITEM DETAILS STYLES ====================
    item_container: {
        padding: 4,
        width: '100%',
    },
    item_row1: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 4,
        marginBottom: 14,
        flexWrap: 'wrap',
        gap: 8,

    },
    item_row2: {
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
        marginBottom: 14,
    },
    item_row3: {
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
        marginBottom: 14,
    },
    item_row4: {
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
        marginBottom: 8,
    },
    item_row5: {
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 14,
    },
    item_chooseBox: {
        flex: 1,
        height: 50,
        width: 70,
        marginLeft: 8,
        marginRight: 8,
    },
    item_chooseButton: {
        backgroundColor: '#02096A',
        height: '100%',
        width: '150',
        borderRadius: 10,
        justifyContent: 'center',
        alignItems: 'center',
    },
    item_titleText: {
        fontSize: 28,
        fontWeight: 'bold',
        color: 'red',
        textAlign: 'center',
        marginBottom: 8,
        paddingHorizontal: 8,
        flex: 4,
    },
    item_input: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 10,
        paddingHorizontal: 10,
        paddingVertical: 8,
        fontSize: 14,
        height: 55
    },
    item_dropdown: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 10,
        paddingHorizontal: 10,
        paddingVertical: 8,
        height: 55,
        justifyContent: 'center',
    },
    item_totalPriceContainer: {
        padding: 10,
        backgroundColor: 'transparent',
        borderRadius: 10,
        marginHorizontal: 4,
    },
    item_totalPriceText: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#000',
    },
    item_actionButton: {
        backgroundColor: '#02096A',
        paddingVertical: 12,
        borderRadius: 9,
        alignItems: 'center',
        justifyContent: 'center',
        marginHorizontal: 4,
    },
    item_actionButtonText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 14,
    },
    errorText: {
        color: 'red',
        fontSize: 12,
        marginTop: 2,
        marginLeft: 4,
    },

    // ==================== TABLE STYLES ====================
    table_container: {
        width: '100%',
        height: 220,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 5,
        marginBottom: 10,
        padding: 8,
    },
    table_header: {
        backgroundColor: 'rgb(2, 9, 106)',
    },
    table_headerText: {
        color: 'white',
        fontWeight: 'bold',
    },

    // ==================== SUMMARY SECTION STYLES ====================
    summary_row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: 8,
        paddingHorizontal: 16,
        gap: 8,
    },
    summary_deleteButton: {
        backgroundColor: 'red',
        paddingHorizontal: 15,
        paddingVertical: 10,
        borderRadius: 8,
        alignSelf: 'center',
        marginLeft: 8,
    },
    summary_box: {
        paddingVertical: 14,
        flex: 1,
    },
    summary_detailRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
        flexWrap: 'wrap',
        gap: 8,
    },
    summary_label: {
        width: 60,
        fontSize: 14,
        fontWeight: 'bold',
        fontFamily: 'Times New Roman',
    },
    summary_valueBox: {
        width: 70,
        paddingHorizontal: 10,
        paddingVertical: 5,
        borderWidth: 1,
        borderColor: '#000',
        borderRadius: 5,
        marginRight: 8,
    },
    summary_valueText: {
        fontSize: 14,
        fontWeight: 'bold',
        fontFamily: 'Times New Roman',
    },
});

export default BillingScreen;
