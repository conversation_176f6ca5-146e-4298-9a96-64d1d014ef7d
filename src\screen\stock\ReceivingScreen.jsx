import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    TextInput,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import CheckBox from '@react-native-community/checkbox';
import { DataTable } from 'react-native-paper';
import Navbar from '../../components/Navbar';
import { bearerToken, branchId, branchName } from '../../globals';

const ReceivingScreen = () => {
    const [currentTime, setCurrentTime] = useState('');
    const [selectedMenu, setSelectedMenu] = useState('Stock');
    const [selectedScrollOption, setSelectedScrollOption] = useState('');
    const [selectedTransferType, setSelectedTransferType] = useState('');
    const [orderNumber, setOrderNumber] = useState('');
    const [invoiceNumber, setInvoiceNumber] = useState('');
    const [lotNumber, setLotNumber] = useState('');

    // Location & Vehicle Details states
    const [location, setLocation] = useState('');
    const [isTransit, setIsTransit] = useState(false);
    const [selectedVehicleType, setSelectedVehicleType] = useState('');
    const [selectedAddress, setSelectedAddress] = useState('');
    const [distance, setDistance] = useState('');
    const [vehicleNumber, setVehicleNumber] = useState('');
    const [driverName, setDriverName] = useState('');
    const [remarks, setRemarks] = useState('');

    // Item Details states
    const [itemLocation, setItemLocation] = useState('');
    const [itemBatch, setItemBatch] = useState('');
    const [selectedPort, setSelectedPort] = useState('');
    const [itemWeight, setItemWeight] = useState('');
    const [itemNos, setItemNos] = useState('');
    const [vmNos, setVmNos] = useState('');
    const [vmWeight, setVmWeight] = useState('');
    const [tableData, setTableData] = useState([
        { id: 1, lineNumber: '001', itemName: 'Item A', nos: '5', kgs: '10.5', remarks: 'Good quality' },
        { id: 2, lineNumber: '002', itemName: 'Item B', nos: '3', kgs: '7.2', remarks: 'Inspect before use' }
    ]);
    const [selectedRows, setSelectedRows] = useState([]);

    const businessDate = new Date().toLocaleDateString();

    const menuOptions = [
        'Billing',
        'Stock',
        'Logistics',
        'Finance',
        'HR',
        'Utils',
        'Reports',
        'Exit',
    ];

    useEffect(() => {
        const interval = setInterval(() => {
            const now = new Date();
            setCurrentTime(now.toLocaleTimeString());
        }, 1000);
        return () => clearInterval(interval);
    }, []);

    const handleMenuPress = (option) => {
        if (option === 'Exit') {
            // Replace with navigation to login
            console.log('Navigating to login screen...');
        } else {
            setSelectedMenu(option);
        }
    };
    return (
        <View style={styles.container}>
            {/* <Navbar/> */}
            <Navbar/>
            {/* Scroll Options */}
            <View style={styles.scrollOptions_container}>

                <View style={styles.scrollOptions_row}>
                    {/* Back Arrow with Receiving Text */}
                    <TouchableOpacity style={styles.scrollOptions_backContainer}>
                        {/*<Text style={styles.scrollOptions_backArrow}>←</Text>*/}
                        <Text style={styles.scrollOptions_screenTitle}>Receiving</Text>
                    </TouchableOpacity>

                    {/* Action Buttons */}
                    <View style={styles.scrollOptions_buttonsContainer}>
                        {['New', 'Save', 'View', 'Cancel'].map((option, index) => {
                            const isSelected = selectedScrollOption === option;
                            let buttonStyle = [styles.scrollOptions_button];

                            if (option === 'Cancel') {
                                buttonStyle.push({
                                    backgroundColor: isSelected ? '#FE0000' : '#FF3333',
                                });
                            } else if (option === 'Save') {
                                buttonStyle.push({
                                    backgroundColor: isSelected ? '#02720F' : '#02A515',
                                });
                            } else {
                                buttonStyle.push({
                                    backgroundColor: isSelected ? '#02096A' : '#DEDDDD',
                                });
                            }

                            return (
                                <View style={styles.scrollOptions_buttonWrapper} key={index}>
                                    <TouchableOpacity
                                        style={buttonStyle}
                                        onPress={() => setSelectedScrollOption(option)}
                                    >
                                        <Text style={[
                                            styles.scrollOptions_buttonText,
                                            { color: isSelected ? 'white' : 'black' },
                                        ]}>
                                            {option}
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            );
                        })}
                    </View>
                </View>
            </View>
            {/* Indent Details Container */}
            <View style={styles.indentDetails_container}>
                {/* Row 1: Indent Details Title */}
                <View style={styles.indentDetails_row}>
                    <Text style={styles.indentDetails_title}>Indent details</Text>
                </View>

                {/* Row 2: Transfer Type Dropdown, Choose Order, Select Button */}
                <View style={styles.indentDetails_inputRow}>
                    <View style={styles.indentDetails_transferTypeContainer}>
                        <Dropdown
                            data={[
                                { label: 'IB Branch Transfer', value: 'IB Branch Transfer' },
                             
                            ]}
                            labelField="label"
                            valueField="value"
                            placeholder="Select Transfer Type"
                            value={selectedTransferType}
                            onChange={(item) => setSelectedTransferType(item.value)}
                            style={styles.indentDetails_dropdown}
                        />
                    </View>

                    <TextInput
                        style={styles.indentDetails_textField}
                        placeholder="Choose order"
                        value={orderNumber}
                        onChangeText={setOrderNumber}
                    />

                    <TouchableOpacity style={styles.indentDetails_selectButton}>
                        <Text style={styles.indentDetails_selectButtonText}>Select</Text>
                    </TouchableOpacity>
                </View>

                {/* Row 3: Invoice, Lot */}
                <View style={styles.indentDetails_inputRow}>
                    <TextInput
                        style={styles.indentDetails_textField}
                        placeholder="Invoice"
                        value={invoiceNumber}
                        onChangeText={setInvoiceNumber}
                    />

                    <TextInput
                        style={styles.indentDetails_textField}
                        placeholder="Lot"
                        value={lotNumber}
                        onChangeText={setLotNumber}
                    />
                </View>
            </View>

            {/* Location & Vehicle Details Container */}
            <View style={styles.locationVehicle_container}>
                {/* Row 1: Location & Vehicle Details Title */}
                <View style={styles.locationVehicle_row}>
                    <Text style={styles.locationVehicle_title}>Location & Vehicle details</Text>
                </View>

                {/* Row 2: Choose Location, Select Button, Transit Button */}
                <View style={styles.locationVehicle_inputRow}>
                    <TextInput
                        style={styles.locationVehicle_textField}
                        placeholder="Choose location"
                        value={location}
                        onChangeText={setLocation}
                    />

                    <TouchableOpacity style={styles.locationVehicle_selectButton}>
                        <Text style={styles.locationVehicle_buttonText}>Select</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[
                            styles.locationVehicle_transitButton,
                            { backgroundColor: isTransit ? '#02096A' : '#DEDDDD' }
                        ]}
                        onPress={() => setIsTransit(!isTransit)}
                    >
                        <Text style={[
                            styles.locationVehicle_buttonText,
                            { color: isTransit ? 'white' : 'black' }
                        ]}>
                            Transit
                        </Text>
                    </TouchableOpacity>
                </View>

                {/* Row 3: Vehicle Type Dropdown, Address Dropdown, New Button */}
                <View style={styles.locationVehicle_inputRow}>
                    <View style={styles.locationVehicle_dropdownContainer}>
                        <Dropdown
                            data={[
                                { label: 'Car', value: 'Car' },
                                { label: 'Truck', value: 'Truck' },
                                { label: 'Van', value: 'Van' },
                                { label: 'Motorcycle', value: 'Motorcycle' },
                            ]}
                            labelField="label"
                            valueField="value"
                            placeholder="Select vehicle type"
                            value={selectedVehicleType}
                            onChange={(item) => setSelectedVehicleType(item.value)}
                            style={styles.locationVehicle_dropdown}
                        />
                    </View>

                    <View style={styles.locationVehicle_dropdownContainer}>
                        <Dropdown
                            data={[
                                { label: 'Address 1', value: 'Address 1' },
                                { label: 'Address 2', value: 'Address 2' },
                                { label: 'Address 3', value: 'Address 3' },
                            ]}
                            labelField="label"
                            valueField="value"
                            placeholder="Address"
                            value={selectedAddress}
                            onChange={(item) => setSelectedAddress(item.value)}
                            style={styles.locationVehicle_dropdown}
                        />
                    </View>

                    <TouchableOpacity style={styles.locationVehicle_newButton}>
                        <Text style={styles.locationVehicle_buttonText}>New</Text>
                    </TouchableOpacity>
                </View>

                {/* Row 4: Distance, Vehicle Number, Driver */}
                <View style={styles.locationVehicle_inputRow}>
                    <TextInput
                        style={styles.locationVehicle_textField}
                        placeholder="Distance"
                        value={distance}
                        onChangeText={setDistance}
                        keyboardType="numeric"
                    />

                    <TextInput
                        style={styles.locationVehicle_textField}
                        placeholder="Vehicle number"
                        value={vehicleNumber}
                        onChangeText={setVehicleNumber}
                    />

                    <TextInput
                        style={styles.locationVehicle_textField}
                        placeholder="Driver"
                        value={driverName}
                        onChangeText={setDriverName}
                    />
                </View>

                {/* Row 5: Remarks, Remove Button */}
                <View style={styles.locationVehicle_inputRow}>
                    <TextInput
                        style={[styles.locationVehicle_textField, { flex: 3 }]}
                        placeholder="Add remarks"
                        value={remarks}
                        onChangeText={setRemarks}
                        multiline
                    />

                    <TouchableOpacity style={styles.locationVehicle_removeButton}>
                        <Text style={styles.locationVehicle_buttonText}>Remove</Text>
                    </TouchableOpacity>
                </View>
            </View>


            {/* Item Details Container */}
            <View style={styles.itemDetails_container}>
                {/* Row 1: Item Details Title */}
                <View style={styles.itemDetails_row}>
                    <Text style={styles.itemDetails_title}>Item details</Text>
                </View>

                {/* Row 2: Choose Location, Select Button, Choose Batch, Select Button, Select Port */}
                <View style={styles.itemDetails_inputRow}>
                    <TextInput
                        style={styles.itemDetails_textField}
                        placeholder="Choose location"
                        value={itemLocation}
                        onChangeText={setItemLocation}
                    />

                    <TouchableOpacity style={styles.itemDetails_selectButton}>
                        <Text style={styles.itemDetails_buttonText}>Select</Text>
                    </TouchableOpacity>

                    <TextInput
                        style={styles.itemDetails_textField}
                        placeholder="Choose batch"
                        value={itemBatch}
                        onChangeText={setItemBatch}
                    />

                    <TouchableOpacity style={styles.itemDetails_selectButton}>
                        <Text style={styles.itemDetails_buttonText}>Select</Text>
                    </TouchableOpacity>

                    <View style={styles.itemDetails_dropdownContainer}>
                        <Dropdown
                            data={[
                                { label: 'Port 1', value: 'Port 1' },
                                { label: 'Port 2', value: 'Port 2' },
                                { label: 'Port 3', value: 'Port 3' },
                            ]}
                            labelField="label"
                            valueField="value"
                            placeholder="Select port"
                            value={selectedPort}
                            onChange={(item) => setSelectedPort(item.value)}
                            style={styles.itemDetails_dropdown}
                        />
                    </View>
                </View>

                {/* Row 3: Wt(kg), Nos, VM Nos, VM Wt(kg) */}
                <View style={styles.itemDetails_inputRow}>
                    <TextInput
                        style={styles.itemDetails_textField}
                        placeholder="Wt(kg)"
                        value={itemWeight}
                        onChangeText={setItemWeight}
                        keyboardType="numeric"
                    />

                    <TextInput
                        style={styles.itemDetails_textField}
                        placeholder="Nos"
                        value={itemNos}
                        onChangeText={setItemNos}
                        keyboardType="numeric"
                    />

                    <TextInput
                        style={styles.itemDetails_textField}
                        placeholder="VM Nos"
                        value={vmNos}
                        onChangeText={setVmNos}
                        keyboardType="numeric"
                    />

                    <TextInput
                        style={styles.itemDetails_textField}
                        placeholder="VM Wt(kg)"
                        value={vmWeight}
                        onChangeText={setVmWeight}
                        keyboardType="numeric"
                    />
                </View>

                {/* Row 4: Add Button, Remove Button */}
                <View style={styles.itemDetails_buttonRow}>
                    <TouchableOpacity style={styles.itemDetails_addButton}>
                        <Text style={styles.itemDetails_buttonText}>Add</Text>
                    </TouchableOpacity>

                    <TouchableOpacity style={styles.itemDetails_removeButton}>
                        <Text style={styles.itemDetails_buttonText}>Remove</Text>
                    </TouchableOpacity>
                </View>

                {/* Row 5: Table */}
                <View style={styles.itemDetails_tableContainer}>
                    <DataTable>
                        <DataTable.Header style={styles.itemDetails_tableHeader}>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Select</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Line Number</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Item Name</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Nos</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Kgs</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Remarks</Text></DataTable.Title>
                        </DataTable.Header>

                        {tableData.map((item) => (
                            <DataTable.Row key={item.id}>
                                <DataTable.Cell>
                                    <CheckBox
                                        value={selectedRows.includes(item.id)}
                                        onValueChange={(newValue) => {
                                            if (newValue) {
                                                setSelectedRows([...selectedRows, item.id]);
                                            } else {
                                                setSelectedRows(selectedRows.filter(id => id !== item.id));
                                            }
                                        }}
                                    />
                                </DataTable.Cell>
                                <DataTable.Cell>{item.lineNumber}</DataTable.Cell>
                                <DataTable.Cell>{item.itemName}</DataTable.Cell>
                                <DataTable.Cell>{item.nos}</DataTable.Cell>
                                <DataTable.Cell>{item.kgs}</DataTable.Cell>
                                <DataTable.Cell>{item.remarks}</DataTable.Cell>
                            </DataTable.Row>
                        ))}
                    </DataTable>
                </View>

                {/* Row 6: Delete Selected Row Button */}
                <View style={styles.itemDetails_deleteButtonContainer}>
                    <TouchableOpacity
                        style={styles.itemDetails_deleteButton}
                        onPress={() => {
                            if (selectedRows.length > 0) {
                                setTableData(tableData.filter(item => !selectedRows.includes(item.id)));
                                setSelectedRows([]);
                            }
                        }}
                    >
                        <Text style={styles.itemDetails_buttonText}>Delete Selected Row</Text>
                    </TouchableOpacity>
                </View>
            </View>






        </View>
    );


};

const styles = StyleSheet.create({
    // ==================== COMMON STYLES ====================
    container: {
        flex: 1,
    },

    // ==================== APP BAR STYLES ====================
    appBar_container: {
        backgroundColor: '#02096A',
        paddingTop: 10,
        paddingBottom: 10,
    },
    appBar_branchRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        marginBottom: 5,
    },
    appBar_branchText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },

    // ==================== MENU BUTTON STYLES ====================
    menu_row: {
        paddingHorizontal: 5,
        alignItems: 'center',
    },
    menu_button: {
        paddingHorizontal: 10,
        paddingVertical: 5,
        marginHorizontal: 5,
        borderRadius: 5,
    },
    menu_text: {
        fontSize: 14,
        fontWeight: 'bold',
    },

    // ==================== PAGE CONTENT STYLES ====================
    pageContent: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#E6E6E6',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
    },

    // ==================== SCROLL OPTIONS STYLES ====================
    scrollOptions_container: {
        backgroundColor: '#E6E6E6',
        paddingVertical: 8,
        marginTop: 0, // Ensure no margin at the top
    },
    scrollOptions_row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 10,
    },
    scrollOptions_backContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    scrollOptions_backArrow: {
        fontSize: 20,
        fontWeight: 'bold',
        marginRight: 5,
    },
    scrollOptions_screenTitle: {
        fontSize: 22,
        fontWeight: 'bold',
        color: '#02096A',
    },
    scrollOptions_buttonsContainer: {
        flexDirection: 'row',
        flex: 1,
        justifyContent: 'flex-end',
    },
    scrollOptions_buttonWrapper: {
        width: '22%',
        marginHorizontal: 5,
    },
    scrollOptions_button: {
        height: 60,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 10,
        
    },
    scrollOptions_buttonText: {
        fontSize: 18,
        fontWeight: 'bold',
    },

    // ==================== INDENT DETAILS STYLES ====================
    indentDetails_container: {
        backgroundColor: '#EBEBEB',
        padding: 12,
        marginHorizontal: 8,
        marginTop: 8,
        marginBottom: 4,
        borderRadius: 10,
    },
    indentDetails_row: {
        marginBottom: 8,
    },
    indentDetails_title: {
        fontSize: 16,
        fontWeight: 'bold',
        color: 'black',
        marginBottom: 5,
    },
    indentDetails_transferTypeContainer: {
        flex: 1,
        minWidth: 120,
    },
    indentDetails_dropdown: {
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
    },
    indentDetails_inputRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 10,
        marginBottom: 8,
    },
    indentDetails_textField: {
        flex: 1,
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
        minWidth: 120,
    },
    indentDetails_selectButton: {
        height: 50,
        backgroundColor: '#041C44',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    indentDetails_selectButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },

    // ==================== LOCATION & VEHICLE DETAILS STYLES ====================
    locationVehicle_container: {
        backgroundColor: '#EBEBEB',
        padding: 12,
        marginHorizontal: 8,
        marginTop: 4,
        marginBottom: 4,
        borderRadius: 10,
    },
    locationVehicle_row: {
        marginBottom: 8,
    },
    locationVehicle_title: {
        fontSize: 16,
        fontWeight: 'bold',
        color: 'black',
        marginBottom: 5,
    },
    locationVehicle_inputRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 10,
        marginBottom: 8,
    },
    locationVehicle_textField: {
        flex: 1,
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
        minWidth: 120,
    },
    locationVehicle_dropdownContainer: {
        flex: 1,
        minWidth: 120,
    },
    locationVehicle_dropdown: {
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
    },
    locationVehicle_selectButton: {
        height: 50,
        backgroundColor: '#041C44',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    locationVehicle_transitButton: {
        height: 50,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    locationVehicle_newButton: {
        height: 50,
        backgroundColor: '#097215',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    locationVehicle_removeButton: {
        height: 50,
        backgroundColor: '#FF0000',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    locationVehicle_buttonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },

    // ==================== ITEM DETAILS STYLES ====================
    itemDetails_container: {
        backgroundColor: '#EBEBEB',
        padding: 12,
        marginHorizontal: 8,
        marginTop: 4,
        marginBottom: 8,
        borderRadius: 10,
    },
    itemDetails_row: {
        marginBottom: 8,
    },
    itemDetails_title: {
        fontSize: 16,
        fontWeight: 'bold',
        color: 'black',
        marginBottom: 5,
    },
    itemDetails_inputRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 10,
        marginBottom: 8,
    },
    itemDetails_textField: {
        flex: 1,
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
        minWidth: 100,
    },
    itemDetails_dropdownContainer: {
        flex: 1,
        minWidth: 120,
    },
    itemDetails_dropdown: {
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
    },
    itemDetails_buttonRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
    itemDetails_selectButton: {
        height: 50,
        backgroundColor: '#041C44',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    itemDetails_addButton: {
        flex: 1,
        height: 50,
        backgroundColor: '#02720F',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 10,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    itemDetails_removeButton: {
        flex: 1,
        height: 50,
        backgroundColor: '#FF0000',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: 10,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    itemDetails_buttonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },
    itemDetails_tableContainer: {
        marginBottom: 8,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        backgroundColor: 'white',
    },
    itemDetails_tableHeader: {
        backgroundColor: '#041C44',
    },
    itemDetails_tableHeaderText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },
    itemDetails_deleteButtonContainer: {
        alignItems: 'center',
        marginBottom: 8,
    },
    itemDetails_deleteButton: {
        height: 50,
        backgroundColor: '#FF0000',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 20,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
});

export default ReceivingScreen;
