// saveBranchTransferAPI.js
import axios from 'axios';

export const saveBranchTransfer = async (token, loginBranchId, loginUserId, businessDate, transferData) => {
  try {
    const now = new Date();
    const systemDate = now.toISOString();

    // Format business date with 'T00:00:00.000Z'
    const [day, month, year] = businessDate.split('-');
    const formattedBusinessDate = `${year}-${month}-${day}T00:00:00.000Z`;

    const payload = {
      branchID: loginBranchId,
      fundTransferId: "",
      transferToBranchType: "ST",
      remarks: "",
      hoHead: "",
      businessDate: formattedBusinessDate,
      deleted: "N",
      isO_Number: "",
      posted: true,
      createdUserId: loginUserId,
      createdDate: systemDate,
      modifiedUserId: "",
      modifiedDate: "1753-01-01T00:00:00.000Z",
      deletedUserId: "",
      deletedDate: "1753-01-01T00:00:00.000Z",
      postedUserId: "",
      postedDate: "1753-01-01T00:00:00.000Z",
      fundTransferDtls: transferData.map(item => ({
        lineNumber: item.lineNumber,
        branchName: item.branchName,
        bankAccountName: "",
        remarks: item.remarks,
        amount: item.amount,
        vehicleNo: item.vehicleNumber || "",
        driverName: item.driverName || "",
        orderedBy: "",
        refTransID: "",
        refTransType: "",
        refLineNumber: "",
        refPGId: "",
        deleted: "N",
        createdUserId: loginUserId,
        createdDate: systemDate,
        modifiedUserId: "",
        modifiedDate: "1753-01-01T00:00:00.000Z",
        deletedUserId: "",
        deletedDate: "1753-01-01T00:00:00.000Z",
        toBranchID: item.branchId, // You must include branchId in each item
        paymentGatewayID: "CAASH",
        dml: "i",
        accountNumber: ""
      }))
    };

     console.log('Sending Payload:', JSON.stringify(payload, null, 2));

    const response = await axios.post(
      'https://retailuat.abisaio.com:9001/api/FT2',
      payload,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('API Response:', response.data);

    return response.data;
  } catch (error) {
    console.error('Save Branch Transfer API error:', error);
    throw error;
  }
};
