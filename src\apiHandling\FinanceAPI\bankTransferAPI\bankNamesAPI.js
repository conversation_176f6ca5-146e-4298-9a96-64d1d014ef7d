// bankAccountsAPI.js
import axios from 'axios';

/**
 * Fetches bank account list from the new API
 * @param {string} bearerToken - The bearer authentication token
 * @returns {Promise<Array<{ label: string, value: string }>>}
 */
export const fetchBankAccounts = async (bearerToken) => {
  try {
    const response = await axios.get(
      'https://retailuat.abisibg.com/api/v1/bankkey',
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
        },
      }
    );

    if (Array.isArray(response.data)) {
      return response.data.map((item) => ({
        label: item.AccountNumber,
        value: item.AccountNumber, // required by Dropdown component
      }));
    } else {
      console.error('Unexpected response format from bank accounts API:', response.data);
      return [];
    }
  } catch (error) {
    console.error('Error fetching bank accounts:', error);
    return [];
  }
};
