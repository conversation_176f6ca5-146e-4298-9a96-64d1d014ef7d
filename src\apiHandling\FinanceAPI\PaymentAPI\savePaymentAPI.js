// savePaymentAPI.js

import axios from 'axios';

export const saveDailyExpenses = async (bearerToken, loginBranchID, loginUserID, businessDate, tableData) => {
  try {
    const currentISTDate = new Date().toISOString(); // We'll assume backend accepts ISO format

    const payload = {
      dailyExpenseHdrid: "",
      branchID: loginBranchID,
      remarks: "", // Optional top-level remarks
      totalAmount: tableData.reduce((sum, item) => sum + parseFloat(item.amount || 0), 0),
      businessDate: `${businessDate}T00:00:00.000Z`,
      deleted: "N",
      isTripExpense: true,
      vehicleTripID: "",
      isO_Number: "",
      createdUserId: loginUserID,
      createdDate: currentISTDate,
      modifiedUserId: "",
      modifiedDate: "1753-01-01T00:00:00.000Z",
      deletedUserId: "",
      deletedDate: "1753-01-01T00:00:00.000Z",
      expenseDtls: tableData.map(row => ({
        lineNumber: row.lineNumber,
        expenseHeadName: row.expenseName,
        expenseGroupName: row.expenseGroupName,
        remarks: row.remark,
        billMonth: row.billMonth,
        unitsConsumed: 0,
        pumpOdoReading: Number(row.pumpOdoReading) || 0,
        paymentMode: row.paymentMode,
        amount: Number(row.amount),
        orderedBy: "",
        vehicleNo: "",
        deleted: "N",
        expenseHeadId: row.expenseId,
        paymentGateWayID: row.paymentGatewayId, // If applicable
        expenseGroupID: row.expenseGroupId,
        createdUserId: loginUserID,
        createdDate: currentISTDate,
        modifiedUserId: "",
        modifiedDate: "1753-01-01T00:00:00.000Z",
        deletedUserId: "",
        deletedDate: "1753-01-01T00:00:00.000Z",
        dml: "i"
      }))
    };

    console.log("Payload being sent to API:", JSON.stringify(payload, null, 2));

    const response = await axios.post(
      'https://retailuat.abisaio.com:9001/api/EXPN2',
      payload,
      {
        headers: {
          Authorization: `Bearer ${bearerToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    console.log("API Response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Error saving daily expenses:", error);
    throw error;
  }
};
