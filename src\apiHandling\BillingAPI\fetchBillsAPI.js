// src/apiHandling/fetchBillsAPI.js
import axios from 'axios';

export const fetchBillsByDate = async (branchId, transTypeId, channelId, businessDate, bearerToken) => {
    try {
        const response = await axios.get(
            `https://retailuat.abisibg.com/api/v1/fetchsale`,
            {
                params: {
                    branchId,
                    TransTypeId: transTypeId,
                    ChannelID: channelId,
                    businessDate,
                },
                headers: {
                    Authorization: `Bearer ${bearerToken}`,
                },
            }
        );
        return response.data;
    } catch (error) {
        console.error('Error fetching sales bills:', error);
        return [];
    }
};
