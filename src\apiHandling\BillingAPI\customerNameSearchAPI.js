// customerNameSearchAPI.js

import axios from 'axios';

export const customerNameSearchAPI = async (branchId, customerName, bearerToken) => {
  const baseUrl = 'https://retailuat.abisaio.com:9001/api/POSCustomerSerch/Get';
  const url = `${baseUrl}/${branchId}/RT/CUSTNAME/${customerName}`;

  try {
    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${bearerToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (response.status === 200 && response.data?.customers) {
      return {
        success: true,
        count: response.data.count,
        customers: response.data.customers,
      };
    } else {
      return {
        success: false,
        message: 'No customers found.',
      };
    }
  } catch (error) {
    console.error('Error fetching customer data:', error.message);
    return {
      success: false,
      message: error.response?.data?.message || 'Something went wrong.',
    };
  }
};
